import pytest
import tempfile
import os
import hashlib
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from models.database import Base, Video
from services.duplicate_detection_service import DuplicateDetectionService
from services.processing_state_service import ProcessingStateService
from utils.hash_utils import calculate_file_hash, calculate_all_hashes


@pytest.fixture
def test_db():
    """Create a test database session"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = TestingSessionLocal()
    yield db
    db.close()


@pytest.fixture
def sample_video_file():
    """Create a temporary video file for testing"""
    with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as f:
        # Write some dummy content
        f.write(b"fake video content for testing")
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    if os.path.exists(temp_path):
        os.unlink(temp_path)


@pytest.fixture
def duplicate_service(test_db):
    """Create a duplicate detection service with test database"""
    return DuplicateDetectionService(test_db)


@pytest.fixture
def state_service(test_db):
    """Create a processing state service with test database"""
    return ProcessingStateService(test_db)


class TestHashUtils:
    """Test hash utility functions"""
    
    def test_calculate_file_hash(self, sample_video_file):
        """Test file hash calculation"""
        hash_value = calculate_file_hash(sample_video_file)
        assert hash_value is not None
        assert len(hash_value) == 64  # SHA256 hash length
        
        # Test with different algorithm
        md5_hash = calculate_file_hash(sample_video_file, algorithm="md5")
        assert md5_hash is not None
        assert len(md5_hash) == 32  # MD5 hash length
    
    def test_calculate_file_hash_nonexistent(self):
        """Test file hash calculation with non-existent file"""
        hash_value = calculate_file_hash("/nonexistent/file.mp4")
        assert hash_value is None
    
    @patch('utils.hash_utils.cv2.VideoCapture')
    def test_calculate_content_hash(self, mock_cv2, sample_video_file):
        """Test content hash calculation"""
        # Mock OpenCV video capture
        mock_cap = Mock()
        mock_cap.isOpened.return_value = True
        mock_cap.get.return_value = 100  # frame count
        mock_cap.read.return_value = (True, Mock())
        mock_cv2.return_value = mock_cap
        
        # This would normally fail without proper video, but we're mocking
        # In a real test, you'd need a proper video file
        pass
    
    def test_calculate_all_hashes(self, sample_video_file):
        """Test calculating all hash types"""
        metadata = {"duration": 10.0, "width": 1920, "height": 1080, "fps": 30.0}
        
        with patch('utils.hash_utils.calculate_content_hash') as mock_content:
            mock_content.return_value = "mock_content_hash"
            
            hashes = calculate_all_hashes(sample_video_file, metadata)
            
            assert 'file_hash' in hashes
            assert 'content_hash' in hashes
            assert 'metadata_hash' in hashes
            assert hashes['file_hash'] is not None
            assert hashes['content_hash'] == "mock_content_hash"
            assert hashes['metadata_hash'] is not None


class TestDuplicateDetectionService:
    """Test duplicate detection service"""
    
    def test_check_for_duplicates_no_duplicates(self, duplicate_service, sample_video_file):
        """Test duplicate check when no duplicates exist"""
        with patch('services.duplicate_detection_service.get_video_metadata') as mock_metadata:
            mock_metadata.return_value = {"duration": 10.0, "width": 1920, "height": 1080}
            
            with patch('utils.hash_utils.calculate_all_hashes') as mock_hashes:
                mock_hashes.return_value = {
                    'file_hash': 'unique_hash',
                    'content_hash': 'unique_content',
                    'metadata_hash': 'unique_metadata'
                }
                
                result = duplicate_service.check_for_duplicates(
                    file_path=sample_video_file,
                    original_filename="test.mp4",
                    file_size=1000
                )
                
                assert not result.is_duplicate
                assert result.existing_video is None
                assert result.duplicate_type is None
                assert not result.can_reprocess
    
    def test_check_for_duplicates_exact_match(self, duplicate_service, test_db, sample_video_file):
        """Test duplicate check with exact file hash match"""
        # Create an existing video with same hash
        existing_video = Video(
            filename="existing.mp4",
            original_filename="existing.mp4",
            file_path="/path/to/existing.mp4",
            file_size=1000,
            file_hash="same_hash",
            processing_status="completed"
        )
        test_db.add(existing_video)
        test_db.commit()
        
        with patch('services.duplicate_detection_service.get_video_metadata') as mock_metadata:
            mock_metadata.return_value = {"duration": 10.0}
            
            with patch('utils.hash_utils.calculate_all_hashes') as mock_hashes:
                mock_hashes.return_value = {
                    'file_hash': 'same_hash',
                    'content_hash': 'different_content',
                    'metadata_hash': 'different_metadata'
                }
                
                result = duplicate_service.check_for_duplicates(
                    file_path=sample_video_file,
                    original_filename="test.mp4",
                    file_size=1000
                )
                
                assert result.is_duplicate
                assert result.existing_video.id == existing_video.id
                assert result.duplicate_type == "exact"
                assert not result.can_reprocess
    
    def test_check_for_duplicates_deleted_video(self, duplicate_service, test_db, sample_video_file):
        """Test duplicate check with deleted video (can reprocess)"""
        # Create a deleted video with same hash
        deleted_video = Video(
            filename="deleted.mp4",
            original_filename="deleted.mp4",
            file_path="/path/to/deleted.mp4",
            file_size=1000,
            file_hash="same_hash",
            is_deleted=True,
            processing_status="deleted"
        )
        test_db.add(deleted_video)
        test_db.commit()
        
        with patch('services.duplicate_detection_service.get_video_metadata') as mock_metadata:
            mock_metadata.return_value = {"duration": 10.0}
            
            with patch('utils.hash_utils.calculate_all_hashes') as mock_hashes:
                mock_hashes.return_value = {
                    'file_hash': 'same_hash',
                    'content_hash': 'different_content',
                    'metadata_hash': 'different_metadata'
                }
                
                result = duplicate_service.check_for_duplicates(
                    file_path=sample_video_file,
                    original_filename="test.mp4",
                    file_size=1000
                )
                
                assert result.is_duplicate
                assert result.existing_video.id == deleted_video.id
                assert result.duplicate_type == "exact"
                assert result.can_reprocess
    
    def test_get_duplicate_groups_empty(self, duplicate_service):
        """Test getting duplicate groups when none exist"""
        result = duplicate_service.get_duplicate_groups()
        
        assert result.total_groups == 0
        assert result.total_duplicates == 0
        assert len(result.groups) == 0
    
    def test_get_duplicate_groups_with_duplicates(self, duplicate_service, test_db):
        """Test getting duplicate groups with actual duplicates"""
        # Create videos with same hash
        video1 = Video(
            filename="video1.mp4",
            original_filename="video1.mp4",
            file_path="/path/to/video1.mp4",
            file_size=1000,
            file_hash="duplicate_hash"
        )
        video2 = Video(
            filename="video2.mp4",
            original_filename="video2.mp4",
            file_path="/path/to/video2.mp4",
            file_size=1000,
            file_hash="duplicate_hash"
        )
        
        test_db.add(video1)
        test_db.add(video2)
        test_db.commit()
        
        result = duplicate_service.get_duplicate_groups()
        
        assert result.total_groups == 1
        assert result.total_duplicates == 2
        assert len(result.groups) == 1
        assert result.groups[0].hash_value == "duplicate_hash"
        assert result.groups[0].total_count == 2


class TestProcessingStateService:
    """Test processing state management service"""
    
    def test_can_start_processing_new_video(self, state_service, test_db):
        """Test if new video can start processing"""
        video = Video(
            filename="new.mp4",
            original_filename="new.mp4",
            file_path="/path/to/new.mp4",
            file_size=1000,
            processing_status="pending"
        )
        test_db.add(video)
        test_db.commit()
        
        can_process, reason = state_service.can_start_processing(video.id)
        
        assert can_process
        assert "can be processed" in reason
    
    def test_can_start_processing_completed_video(self, state_service, test_db):
        """Test if completed video can start processing"""
        video = Video(
            filename="completed.mp4",
            original_filename="completed.mp4",
            file_path="/path/to/completed.mp4",
            file_size=1000,
            processing_status="completed"
        )
        test_db.add(video)
        test_db.commit()
        
        can_process, reason = state_service.can_start_processing(video.id)
        
        assert not can_process
        assert "already processed" in reason
    
    def test_can_start_processing_deleted_video(self, state_service, test_db):
        """Test if deleted video can start processing"""
        video = Video(
            filename="deleted.mp4",
            original_filename="deleted.mp4",
            file_path="/path/to/deleted.mp4",
            file_size=1000,
            is_deleted=True,
            processing_status="deleted"
        )
        test_db.add(video)
        test_db.commit()
        
        can_process, reason = state_service.can_start_processing(video.id)
        
        assert not can_process
        assert "deleted" in reason
    
    def test_start_processing_success(self, state_service, test_db):
        """Test starting processing successfully"""
        video = Video(
            filename="test.mp4",
            original_filename="test.mp4",
            file_path="/path/to/test.mp4",
            file_size=1000,
            processing_status="pending"
        )
        test_db.add(video)
        test_db.commit()
        
        success = state_service.start_processing(video.id)
        
        assert success
        
        # Refresh video from database
        test_db.refresh(video)
        assert video.processing_status == "processing"
        assert video.processing_progress == 0
    
    def test_complete_processing_success(self, state_service, test_db):
        """Test completing processing successfully"""
        video = Video(
            filename="test.mp4",
            original_filename="test.mp4",
            file_path="/path/to/test.mp4",
            file_size=1000,
            processing_status="processing"
        )
        test_db.add(video)
        test_db.commit()
        
        success = state_service.complete_processing(video.id, True)
        
        assert success
        
        # Refresh video from database
        test_db.refresh(video)
        assert video.processing_status == "completed"
        assert video.processing_progress == 100
        assert video.processed is True
    
    def test_complete_processing_failure(self, state_service, test_db):
        """Test completing processing with failure"""
        video = Video(
            filename="test.mp4",
            original_filename="test.mp4",
            file_path="/path/to/test.mp4",
            file_size=1000,
            processing_status="processing"
        )
        test_db.add(video)
        test_db.commit()
        
        success = state_service.complete_processing(video.id, False, "Test error")
        
        assert success
        
        # Refresh video from database
        test_db.refresh(video)
        assert video.processing_status == "failed"
        assert video.processed is False
