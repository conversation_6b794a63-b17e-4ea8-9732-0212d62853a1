from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional, Dict, Any, Tuple
import os
from datetime import datetime

from models.database import Video
from models.schemas import DuplicateCheckRequest, DuplicateCheckResponse, DuplicateGroup, DuplicateGroupsResponse
from utils.hash_utils import calculate_all_hashes, compare_hashes, get_similarity_score
from utils.video_utils import get_video_metadata


class DuplicateDetectionService:
    def __init__(self, db: Session):
        self.db = db
        self.similarity_threshold = 0.95  # Threshold for content similarity
    
    def check_for_duplicates(
        self, 
        file_path: str, 
        original_filename: str,
        file_size: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> DuplicateCheckResponse:
        """
        Check if a video file is a duplicate of an existing video.
        
        Args:
            file_path: Path to the video file
            original_filename: Original filename of the video
            file_size: Size of the video file
            metadata: Optional video metadata
            
        Returns:
            DuplicateCheckResponse with duplicate information
        """
        try:
            # Calculate hashes for the new video
            if not metadata:
                metadata = get_video_metadata(file_path)
                
            hashes = calculate_all_hashes(file_path, metadata)
            
            # Check for exact file hash match first (highest priority)
            if hashes['file_hash']:
                exact_duplicate = self._find_by_file_hash(hashes['file_hash'])
                if exact_duplicate:
                    return DuplicateCheckResponse(
                        is_duplicate=True,
                        existing_video=exact_duplicate,
                        duplicate_type="exact",
                        can_reprocess=exact_duplicate.is_deleted,
                        message="Exact duplicate found (same file content)"
                    )
            
            # Check for content hash match (same content, different encoding)
            if hashes['content_hash']:
                content_duplicate = self._find_by_content_hash(hashes['content_hash'])
                if content_duplicate:
                    return DuplicateCheckResponse(
                        is_duplicate=True,
                        existing_video=content_duplicate,
                        duplicate_type="content",
                        can_reprocess=content_duplicate.is_deleted,
                        message="Content duplicate found (same video content)"
                    )
            
            # Check for metadata hash match (similar properties)
            if hashes['metadata_hash']:
                metadata_duplicate = self._find_by_metadata_hash(hashes['metadata_hash'])
                if metadata_duplicate:
                    return DuplicateCheckResponse(
                        is_duplicate=True,
                        existing_video=metadata_duplicate,
                        duplicate_type="metadata",
                        can_reprocess=metadata_duplicate.is_deleted,
                        message="Metadata duplicate found (similar video properties)"
                    )
            
            # Check for filename similarity (lowest priority)
            filename_duplicate = self._find_by_filename_similarity(original_filename, file_size)
            if filename_duplicate:
                return DuplicateCheckResponse(
                    is_duplicate=True,
                    existing_video=filename_duplicate,
                    duplicate_type="filename",
                    can_reprocess=filename_duplicate.is_deleted,
                    message="Filename duplicate found (similar filename and size)"
                )
            
            # No duplicates found
            return DuplicateCheckResponse(
                is_duplicate=False,
                existing_video=None,
                duplicate_type=None,
                can_reprocess=False,
                message="No duplicates found"
            )
            
        except Exception as e:
            print(f"Error checking for duplicates: {e}")
            return DuplicateCheckResponse(
                is_duplicate=False,
                existing_video=None,
                duplicate_type=None,
                can_reprocess=False,
                message=f"Error during duplicate check: {str(e)}"
            )
    
    def _find_by_file_hash(self, file_hash: str) -> Optional[Video]:
        """Find video by exact file hash match."""
        return self.db.query(Video).filter(Video.file_hash == file_hash).first()
    
    def _find_by_content_hash(self, content_hash: str) -> Optional[Video]:
        """Find video by content hash match."""
        return self.db.query(Video).filter(Video.content_hash == content_hash).first()
    
    def _find_by_metadata_hash(self, metadata_hash: str) -> Optional[Video]:
        """Find video by metadata hash match."""
        return self.db.query(Video).filter(Video.metadata_hash == metadata_hash).first()
    
    def _find_by_filename_similarity(self, original_filename: str, file_size: int) -> Optional[Video]:
        """Find video by filename and file size similarity."""
        # Look for videos with same filename and similar file size (within 5%)
        size_tolerance = file_size * 0.05
        min_size = file_size - size_tolerance
        max_size = file_size + size_tolerance
        
        return self.db.query(Video).filter(
            and_(
                Video.original_filename == original_filename,
                Video.file_size >= min_size,
                Video.file_size <= max_size
            )
        ).first()
    
    def update_video_hashes(self, video_id: int) -> bool:
        """
        Calculate and update hashes for an existing video.
        
        Args:
            video_id: ID of the video to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video or not os.path.exists(video.file_path):
                return False
            
            # Get video metadata
            metadata = get_video_metadata(video.file_path)
            
            # Calculate all hashes
            hashes = calculate_all_hashes(video.file_path, metadata)
            
            # Update video record
            video.file_hash = hashes['file_hash']
            video.content_hash = hashes['content_hash']
            video.metadata_hash = hashes['metadata_hash']
            
            self.db.commit()
            return True
            
        except Exception as e:
            print(f"Error updating video hashes for video {video_id}: {e}")
            self.db.rollback()
            return False
    
    def get_duplicate_groups(self, include_deleted: bool = False) -> DuplicateGroupsResponse:
        """
        Get all duplicate groups in the database.
        
        Args:
            include_deleted: Whether to include deleted videos
            
        Returns:
            DuplicateGroupsResponse with all duplicate groups
        """
        try:
            groups = []
            total_duplicates = 0
            
            # Query for file hash duplicates
            file_hash_groups = self._get_hash_groups('file_hash', include_deleted)
            groups.extend(file_hash_groups)
            
            # Query for content hash duplicates
            content_hash_groups = self._get_hash_groups('content_hash', include_deleted)
            groups.extend(content_hash_groups)
            
            # Query for metadata hash duplicates
            metadata_hash_groups = self._get_hash_groups('metadata_hash', include_deleted)
            groups.extend(metadata_hash_groups)
            
            # Calculate total duplicates
            for group in groups:
                total_duplicates += len(group.videos)
            
            return DuplicateGroupsResponse(
                groups=groups,
                total_groups=len(groups),
                total_duplicates=total_duplicates
            )
            
        except Exception as e:
            print(f"Error getting duplicate groups: {e}")
            return DuplicateGroupsResponse(
                groups=[],
                total_groups=0,
                total_duplicates=0
            )
    
    def _get_hash_groups(self, hash_field: str, include_deleted: bool) -> List[DuplicateGroup]:
        """Get duplicate groups for a specific hash field."""
        groups = []
        
        try:
            # Build query
            query = self.db.query(Video)
            if not include_deleted:
                query = query.filter(Video.is_deleted == False)
            
            # Get all videos with non-null hash values
            hash_column = getattr(Video, hash_field)
            videos = query.filter(hash_column.isnot(None)).all()
            
            # Group by hash value
            hash_groups = {}
            for video in videos:
                hash_value = getattr(video, hash_field)
                if hash_value not in hash_groups:
                    hash_groups[hash_value] = []
                hash_groups[hash_value].append(video)
            
            # Create duplicate groups (only groups with more than 1 video)
            for hash_value, video_list in hash_groups.items():
                if len(video_list) > 1:
                    groups.append(DuplicateGroup(
                        hash_value=hash_value,
                        hash_type=hash_field,
                        videos=video_list,
                        total_count=len(video_list)
                    ))
            
        except Exception as e:
            print(f"Error getting {hash_field} groups: {e}")
        
        return groups
    
    def can_reprocess_video(self, video_id: int) -> bool:
        """
        Check if a video can be reprocessed (e.g., if it was deleted).
        
        Args:
            video_id: ID of the video to check
            
        Returns:
            True if video can be reprocessed, False otherwise
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return False
            
            # Can reprocess if video is deleted or processing failed
            return video.is_deleted or video.processing_status == "failed"
            
        except Exception as e:
            print(f"Error checking if video can be reprocessed: {e}")
            return False
    
    def mark_video_for_reprocessing(self, video_id: int) -> bool:
        """
        Mark a video for reprocessing by resetting its processing status.
        
        Args:
            video_id: ID of the video to mark for reprocessing
            
        Returns:
            True if successful, False otherwise
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return False
            
            # Reset processing status
            video.processing_status = "pending"
            video.processing_progress = 0
            video.processed = False
            video.is_deleted = False
            video.deleted_at = None
            
            self.db.commit()
            return True
            
        except Exception as e:
            print(f"Error marking video for reprocessing: {e}")
            self.db.rollback()
            return False
