from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from enum import Enum

from models.database import Video, ProcessingJob


class ProcessingState(Enum):
    """Enumeration of video processing states."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    DUPLICATE = "duplicate"
    DELETED = "deleted"


class ProcessingStateService:
    def __init__(self, db: Session):
        self.db = db
        self.processing_timeout = timedelta(hours=2)  # Timeout for stuck processing jobs
    
    def can_start_processing(self, video_id: int) -> Tuple[bool, str]:
        """
        Check if a video can start processing.
        
        Args:
            video_id: ID of the video to check
            
        Returns:
            Tuple of (can_process, reason)
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return False, "Video not found"
            
            # Check if video is deleted
            if video.is_deleted:
                return False, "Video is deleted"
            
            # Check current processing status
            if video.processing_status == ProcessingState.COMPLETED.value:
                return False, "Video already processed"
            
            if video.processing_status == ProcessingState.PROCESSING.value:
                # Check if processing is stuck (timeout)
                if self._is_processing_stuck(video_id):
                    return True, "Previous processing timed out, can restart"
                return False, "Video is currently being processed"
            
            if video.processing_status == ProcessingState.DUPLICATE.value:
                return False, "Video is marked as duplicate"
            
            # Check for active processing jobs
            active_job = self._get_active_processing_job(video_id)
            if active_job:
                if self._is_job_stuck(active_job):
                    return True, "Previous job timed out, can restart"
                return False, "Video has active processing job"
            
            # Video can be processed
            return True, "Video can be processed"
            
        except Exception as e:
            print(f"Error checking if video can start processing: {e}")
            return False, f"Error: {str(e)}"
    
    def start_processing(self, video_id: int, job_type: str = "full_processing") -> bool:
        """
        Mark a video as starting processing and create a processing job.
        
        Args:
            video_id: ID of the video to start processing
            job_type: Type of processing job
            
        Returns:
            True if successful, False otherwise
        """
        try:
            can_process, reason = self.can_start_processing(video_id)
            if not can_process:
                print(f"Cannot start processing for video {video_id}: {reason}")
                return False
            
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return False
            
            # Cancel any stuck jobs
            self._cancel_stuck_jobs(video_id)
            
            # Update video status
            video.processing_status = ProcessingState.PROCESSING.value
            video.processing_progress = 0
            video.processed = False
            
            # Create new processing job
            job = ProcessingJob(
                video_id=video_id,
                job_type=job_type,
                status="running",
                started_at=datetime.now()
            )
            
            self.db.add(job)
            self.db.commit()
            
            return True
            
        except Exception as e:
            print(f"Error starting processing for video {video_id}: {e}")
            self.db.rollback()
            return False
    
    def update_processing_progress(self, video_id: int, progress: int, status: Optional[str] = None) -> bool:
        """
        Update processing progress for a video.
        
        Args:
            video_id: ID of the video
            progress: Progress percentage (0-100)
            status: Optional status update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return False
            
            video.processing_progress = max(0, min(100, progress))
            
            if status:
                video.processing_status = status
            
            self.db.commit()
            return True
            
        except Exception as e:
            print(f"Error updating processing progress for video {video_id}: {e}")
            self.db.rollback()
            return False
    
    def complete_processing(self, video_id: int, success: bool = True, error_message: Optional[str] = None) -> bool:
        """
        Mark processing as completed for a video.
        
        Args:
            video_id: ID of the video
            success: Whether processing was successful
            error_message: Optional error message if processing failed
            
        Returns:
            True if successful, False otherwise
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return False
            
            # Update video status
            if success:
                video.processing_status = ProcessingState.COMPLETED.value
                video.processing_progress = 100
                video.processed = True
            else:
                video.processing_status = ProcessingState.FAILED.value
                video.processed = False
            
            # Update processing job
            active_job = self._get_active_processing_job(video_id)
            if active_job:
                active_job.status = "completed" if success else "failed"
                active_job.completed_at = datetime.now()
                if error_message:
                    active_job.error_message = error_message
            
            self.db.commit()
            return True
            
        except Exception as e:
            print(f"Error completing processing for video {video_id}: {e}")
            self.db.rollback()
            return False
    
    def mark_as_duplicate(self, video_id: int, duplicate_of_id: Optional[int] = None) -> bool:
        """
        Mark a video as a duplicate.
        
        Args:
            video_id: ID of the video to mark as duplicate
            duplicate_of_id: Optional ID of the original video
            
        Returns:
            True if successful, False otherwise
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return False
            
            video.processing_status = ProcessingState.DUPLICATE.value
            video.processed = False
            
            # Cancel any active processing jobs
            self._cancel_active_jobs(video_id)
            
            self.db.commit()
            return True
            
        except Exception as e:
            print(f"Error marking video as duplicate: {e}")
            self.db.rollback()
            return False
    
    def soft_delete_video(self, video_id: int) -> bool:
        """
        Soft delete a video (mark as deleted without removing from database).
        
        Args:
            video_id: ID of the video to soft delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return False
            
            video.is_deleted = True
            video.deleted_at = datetime.now()
            video.processing_status = ProcessingState.DELETED.value
            
            # Cancel any active processing jobs
            self._cancel_active_jobs(video_id)
            
            self.db.commit()
            return True
            
        except Exception as e:
            print(f"Error soft deleting video: {e}")
            self.db.rollback()
            return False
    
    def restore_video(self, video_id: int) -> bool:
        """
        Restore a soft-deleted video.
        
        Args:
            video_id: ID of the video to restore
            
        Returns:
            True if successful, False otherwise
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return False
            
            video.is_deleted = False
            video.deleted_at = None
            video.processing_status = ProcessingState.PENDING.value
            video.processing_progress = 0
            video.processed = False
            
            self.db.commit()
            return True
            
        except Exception as e:
            print(f"Error restoring video: {e}")
            self.db.rollback()
            return False
    
    def get_processing_statistics(self) -> Dict[str, int]:
        """
        Get statistics about video processing states.
        
        Returns:
            Dictionary with processing state counts
        """
        try:
            stats = {}
            
            # Count videos by processing status
            for state in ProcessingState:
                count = self.db.query(Video).filter(
                    Video.processing_status == state.value
                ).count()
                stats[state.value] = count
            
            # Additional statistics
            stats['total_videos'] = self.db.query(Video).count()
            stats['active_videos'] = self.db.query(Video).filter(
                Video.is_deleted == False
            ).count()
            stats['deleted_videos'] = self.db.query(Video).filter(
                Video.is_deleted == True
            ).count()
            
            return stats
            
        except Exception as e:
            print(f"Error getting processing statistics: {e}")
            return {}
    
    def _get_active_processing_job(self, video_id: int) -> Optional[ProcessingJob]:
        """Get active processing job for a video."""
        return self.db.query(ProcessingJob).filter(
            and_(
                ProcessingJob.video_id == video_id,
                ProcessingJob.status == "running"
            )
        ).first()
    
    def _is_processing_stuck(self, video_id: int) -> bool:
        """Check if video processing is stuck (timed out)."""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video or video.processing_status != ProcessingState.PROCESSING.value:
            return False
        
        # Check if there's an active job that's timed out
        active_job = self._get_active_processing_job(video_id)
        if active_job:
            return self._is_job_stuck(active_job)
        
        return False
    
    def _is_job_stuck(self, job: ProcessingJob) -> bool:
        """Check if a processing job is stuck (timed out)."""
        if not job.started_at:
            return False
        
        time_elapsed = datetime.now() - job.started_at
        return time_elapsed > self.processing_timeout
    
    def _cancel_stuck_jobs(self, video_id: int) -> None:
        """Cancel any stuck processing jobs for a video."""
        active_job = self._get_active_processing_job(video_id)
        if active_job and self._is_job_stuck(active_job):
            active_job.status = "cancelled"
            active_job.completed_at = datetime.now()
            active_job.error_message = "Job timed out"
    
    def _cancel_active_jobs(self, video_id: int) -> None:
        """Cancel all active processing jobs for a video."""
        active_jobs = self.db.query(ProcessingJob).filter(
            and_(
                ProcessingJob.video_id == video_id,
                ProcessingJob.status == "running"
            )
        ).all()
        
        for job in active_jobs:
            job.status = "cancelled"
            job.completed_at = datetime.now()
            job.error_message = "Job cancelled"
