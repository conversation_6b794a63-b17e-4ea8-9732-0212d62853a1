from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional
import os
import shutil
import re

from models.database import Video, Tag, video_tags
from models.schemas import VideoUpdate, VideoFilter
from utils.video_utils import get_video_metadata, generate_thumbnail

class VideoService:
    def __init__(self, db: Session):
        self.db = db

    def _matches_word_boundary(self, text: str, search_words: List[str]) -> bool:
        """Check if any search word matches with word boundaries in the text"""
        if not text:
            return False

        text_lower = text.lower()
        for word in search_words:
            # Create regex pattern for word boundary matching
            # \b ensures word boundaries (start/end of word)
            pattern = r'\b' + re.escape(word.lower()) + r'\b'
            if re.search(pattern, text_lower):
                return True
        return False
    
    def get_videos(
        self, 
        skip: int = 0, 
        limit: int = 50, 
        filters: Optional[VideoFilter] = None
    ) -> List[Video]:
        """Get videos with optional filtering"""
        query = self.db.query(Video)
        
        if filters:
            # Filter by tags
            if filters.tags:
                query = query.join(Video.tags).filter(
                    Tag.name.in_(filters.tags)
                )
            
            # Enhanced smart search with category support
            if filters.search:
                search_query = filters.search.strip()

                # Check for category: syntax
                if search_query.startswith('category:'):
                    category_name = search_query[9:].strip()  # Remove 'category:' prefix
                    if category_name:
                        category_pattern = f"{category_name}:%"
                        query = query.join(Video.tags).filter(
                            Tag.description.ilike(category_pattern)
                        )
                else:
                    # Two-step search: broad SQL search + precise Python filtering
                    search_words = search_query.lower().split()

                    # Step 1: Broad search to get potential matches
                    search_term = f"%{search_query}%"
                    category_pattern = f"{search_query}:%"
                    category_name_pattern = f"%{search_query}%"

                    # Get all potential matches using broad search
                    query = query.outerjoin(Video.tags).filter(
                        or_(
                            Video.title.ilike(search_term),
                            Video.transcript.ilike(search_term),
                            Video.original_filename.ilike(search_term),
                            Tag.description.ilike(category_pattern),
                            Tag.description.ilike(category_name_pattern),
                            Tag.name.ilike(search_term)
                        )
                    ).distinct()
            
            # Filter by language
            if filters.language:
                query = query.filter(Video.transcript_language == filters.language)
            
            # Filter by processing status
            if filters.processed is not None:
                query = query.filter(Video.processed == filters.processed)
            
            # Filter by date range
            if filters.date_from:
                query = query.filter(Video.upload_date >= filters.date_from)
            if filters.date_to:
                query = query.filter(Video.upload_date <= filters.date_to)

            # Filter by category (based on tag descriptions)
            if filters.category:
                category_pattern = f"{filters.category}:%"
                query = query.join(Video.tags).filter(
                    Tag.description.like(category_pattern)
                )
        
        # Order by upload date (newest first)
        query = query.order_by(Video.upload_date.desc())

        # Get the results
        results = query.offset(skip).limit(limit).all()

        # For search queries, apply precise word boundary filtering
        if filters and filters.search and results and not filters.search.startswith('category:'):
            search_words = filters.search.strip().lower().split()
            filtered_results = []

            for video in results:
                # Force load tags to avoid lazy loading issues
                _ = len(video.tags)

                # Check if video matches with word boundaries
                matches = False

                # Check title
                if video.title and self._matches_word_boundary(video.title, search_words):
                    matches = True

                # Check transcript
                if not matches and video.transcript and self._matches_word_boundary(video.transcript, search_words):
                    matches = True

                # Check filename (keep substring matching for filenames)
                if not matches and video.original_filename:
                    filename_lower = video.original_filename.lower()
                    if any(word in filename_lower for word in search_words):
                        matches = True

                # Check tag names and descriptions
                if not matches:
                    for tag in video.tags:
                        # Check tag name with word boundaries
                        if tag.name and self._matches_word_boundary(tag.name, search_words):
                            matches = True
                            break

                        # Check tag description for category matches
                        if tag.description:
                            # Category format matching
                            if any(f"{word}:" in tag.description.lower() for word in search_words):
                                matches = True
                                break
                            # General description matching
                            if self._matches_word_boundary(tag.description, search_words):
                                matches = True
                                break

                if matches:
                    filtered_results.append(video)

            return filtered_results

        # For non-search queries or category searches, ensure tags are loaded
        elif filters and filters.search and results:
            for video in results:
                _ = len(video.tags)

        return results
    
    def get_video_by_id(self, video_id: int) -> Optional[Video]:
        """Get a video by its ID"""
        return self.db.query(Video).filter(Video.id == video_id).first()
    
    def get_video_by_filename(self, filename: str) -> Optional[Video]:
        """Get a video by its filename"""
        return self.db.query(Video).filter(Video.filename == filename).first()
    
    def create_video(
        self, 
        filename: str, 
        original_filename: str, 
        file_path: str, 
        file_size: int,
        title: Optional[str] = None
    ) -> Video:
        """Create a new video record"""
        # Get video metadata
        metadata = get_video_metadata(file_path)
        
        video = Video(
            filename=filename,
            original_filename=original_filename,
            title=title or os.path.splitext(original_filename)[0],
            file_path=file_path,
            file_size=file_size,
            duration=metadata.get('duration'),
            width=metadata.get('width'),
            height=metadata.get('height'),
            fps=metadata.get('fps'),
            processing_status="pending"
        )
        
        self.db.add(video)
        self.db.commit()
        self.db.refresh(video)
        
        # Generate thumbnail asynchronously
        try:
            thumbnail_path = generate_thumbnail(file_path, video.id)
            if thumbnail_path:
                video.thumbnail_path = thumbnail_path
                self.db.commit()
        except Exception as e:
            print(f"Failed to generate thumbnail for video {video.id}: {e}")
        
        return video
    
    def update_video(self, video_id: int, video_update: VideoUpdate) -> Optional[Video]:
        """Update a video's metadata"""
        video = self.get_video_by_id(video_id)
        if not video:
            return None
        
        # Update fields
        if video_update.title is not None:
            video.title = video_update.title
        
        self.db.commit()
        self.db.refresh(video)
        return video
    
    def delete_video(self, video_id: int) -> bool:
        """Delete a video and its associated files"""
        video = self.get_video_by_id(video_id)
        if not video:
            return False

        try:
            # Update tag usage counts before deleting video
            for tag in video.tags:
                tag.usage_count = max(0, tag.usage_count - 1)

            # Delete video file
            if os.path.exists(video.file_path):
                os.remove(video.file_path)

            # Delete thumbnail
            if video.thumbnail_path and os.path.exists(video.thumbnail_path):
                os.remove(video.thumbnail_path)

            # Delete transcript file if exists
            transcript_path = f"transcripts/{video.id}.txt"
            if os.path.exists(transcript_path):
                os.remove(transcript_path)

            # Remove video from database (this will also remove the tag associations)
            self.db.delete(video)
            self.db.commit()

            # Clean up unused tags (optional - remove tags with 0 usage)
            self._cleanup_unused_tags()

            return True
        except Exception as e:
            print(f"Error deleting video {video_id}: {e}")
            self.db.rollback()
            return False

    def _cleanup_unused_tags(self):
        """Remove tags that are no longer used by any videos"""
        try:
            unused_tags = self.db.query(Tag).filter(Tag.usage_count <= 0).all()
            for tag in unused_tags:
                self.db.delete(tag)
            self.db.commit()
            if unused_tags:
                print(f"Cleaned up {len(unused_tags)} unused tags")
        except Exception as e:
            print(f"Error cleaning up unused tags: {e}")
            self.db.rollback()

    def recalculate_tag_usage_counts(self):
        """Recalculate usage counts for all tags based on actual video associations"""
        try:
            all_tags = self.db.query(Tag).all()
            for tag in all_tags:
                # Count actual videos associated with this tag
                actual_count = len(tag.videos)
                tag.usage_count = actual_count

            self.db.commit()
            print(f"Recalculated usage counts for {len(all_tags)} tags")

            # Clean up tags with 0 usage
            self._cleanup_unused_tags()

        except Exception as e:
            print(f"Error recalculating tag usage counts: {e}")
            self.db.rollback()
    
    def update_processing_status(
        self,
        video_id: int,
        status: str,
        transcript: Optional[str] = None,
        language: Optional[str] = None,
        progress: Optional[int] = None
    ) -> bool:
        """Update video processing status, transcript, and progress"""
        video = self.get_video_by_id(video_id)
        if not video:
            return False

        video.processing_status = status
        if status == "completed":
            video.processed = True
            video.processing_progress = 100
        elif progress is not None:
            video.processing_progress = max(0, min(100, progress))  # Clamp between 0-100

        if transcript:
            video.transcript = transcript
        
        if language:
            video.transcript_language = language
        
        self.db.commit()
        return True
    
    def get_videos_count(self, filters: Optional[VideoFilter] = None) -> int:
        """Get total count of videos with optional filtering"""
        query = self.db.query(Video)
        
        if filters:
            # Apply same filters as get_videos
            if filters.tags:
                query = query.join(Video.tags).filter(Tag.name.in_(filters.tags))
            if filters.search:
                search_query = filters.search.strip()

                # Check for category: syntax
                if search_query.startswith('category:'):
                    category_name = search_query[9:].strip()  # Remove 'category:' prefix
                    if category_name:
                        category_pattern = f"{category_name}:%"
                        query = query.join(Video.tags).filter(
                            Tag.description.ilike(category_pattern)
                        )
                else:
                    # Note: For count queries, we use broad search since Python filtering
                    # would require fetching all records. This may return slightly higher
                    # counts than the actual filtered results, but it's more efficient.
                    search_term = f"%{search_query}%"
                    category_pattern = f"{search_query}:%"
                    category_name_pattern = f"%{search_query}%"

                    query = query.outerjoin(Video.tags).filter(
                        or_(
                            Video.title.ilike(search_term),
                            Video.transcript.ilike(search_term),
                            Video.original_filename.ilike(search_term),
                            Tag.description.ilike(category_pattern),
                            Tag.description.ilike(category_name_pattern),
                            Tag.name.ilike(search_term)
                        )
                    )
            if filters.language:
                query = query.filter(Video.transcript_language == filters.language)
            if filters.processed is not None:
                query = query.filter(Video.processed == filters.processed)
            if filters.date_from:
                query = query.filter(Video.upload_date >= filters.date_from)
            if filters.date_to:
                query = query.filter(Video.upload_date <= filters.date_to)
            if filters.category:
                category_pattern = f"{filters.category}:%"
                query = query.join(Video.tags).filter(
                    Tag.description.like(category_pattern)
                )

        return query.count()
