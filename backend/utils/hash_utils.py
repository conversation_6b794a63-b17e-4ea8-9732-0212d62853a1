import hashlib
import os
import cv2
import numpy as np
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from moviepy.editor import VideoFileClip
import tempfile
import subprocess


def calculate_file_hash(file_path: str, algorithm: str = "sha256", chunk_size: int = 8192) -> Optional[str]:
    """
    Calculate hash of file content using specified algorithm.
    
    Args:
        file_path: Path to the file
        algorithm: Hash algorithm ("md5", "sha256", "sha1")
        chunk_size: Size of chunks to read at a time
        
    Returns:
        Hex string of the hash or None if error
    """
    try:
        if not os.path.exists(file_path):
            return None
            
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            while chunk := f.read(chunk_size):
                hash_obj.update(chunk)
                
        return hash_obj.hexdigest()
        
    except Exception as e:
        print(f"Error calculating file hash for {file_path}: {e}")
        return None


def calculate_content_hash(file_path: str, sample_frames: int = 8) -> Optional[str]:
    """
    Calculate content-based hash by sampling video frames.
    This helps detect videos with same content but different encoding.
    
    Args:
        file_path: Path to the video file
        sample_frames: Number of frames to sample for hash calculation
        
    Returns:
        Hash string based on video content or None if error
    """
    try:
        if not os.path.exists(file_path):
            return None
            
        cap = cv2.VideoCapture(file_path)
        if not cap.isOpened():
            return None
            
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if frame_count == 0:
            cap.release()
            return None
            
        # Calculate frame positions to sample
        frame_positions = []
        if frame_count <= sample_frames:
            frame_positions = list(range(frame_count))
        else:
            step = frame_count // sample_frames
            frame_positions = [i * step for i in range(sample_frames)]
            
        frame_hashes = []
        
        for pos in frame_positions:
            cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
            ret, frame = cap.read()
            
            if ret:
                # Resize frame to standard size for consistent hashing
                frame_resized = cv2.resize(frame, (64, 64))
                # Convert to grayscale
                frame_gray = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2GRAY)
                # Calculate hash of frame
                frame_hash = hashlib.md5(frame_gray.tobytes()).hexdigest()
                frame_hashes.append(frame_hash)
                
        cap.release()
        
        if not frame_hashes:
            return None
            
        # Combine all frame hashes
        combined_hash = hashlib.sha256(''.join(frame_hashes).encode()).hexdigest()
        return combined_hash
        
    except Exception as e:
        print(f"Error calculating content hash for {file_path}: {e}")
        return None


def calculate_metadata_hash(metadata: Dict[str, Any]) -> Optional[str]:
    """
    Calculate hash based on video metadata (duration, dimensions, fps).
    This helps detect videos with same content but different file formats.
    
    Args:
        metadata: Dictionary containing video metadata
        
    Returns:
        Hash string based on metadata or None if error
    """
    try:
        # Extract key metadata fields for hashing
        key_fields = ['duration', 'width', 'height', 'fps']
        metadata_values = []
        
        for field in key_fields:
            value = metadata.get(field)
            if value is not None:
                # Round floating point values to avoid minor differences
                if isinstance(value, float):
                    value = round(value, 2)
                metadata_values.append(str(value))
            else:
                metadata_values.append('None')
                
        # Create hash from combined metadata
        metadata_string = '|'.join(metadata_values)
        metadata_hash = hashlib.md5(metadata_string.encode()).hexdigest()
        
        return metadata_hash
        
    except Exception as e:
        print(f"Error calculating metadata hash: {e}")
        return None


def calculate_all_hashes(file_path: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Optional[str]]:
    """
    Calculate all types of hashes for a video file.
    
    Args:
        file_path: Path to the video file
        metadata: Optional metadata dictionary
        
    Returns:
        Dictionary containing all calculated hashes
    """
    hashes = {
        'file_hash': None,
        'content_hash': None,
        'metadata_hash': None
    }
    
    try:
        # Calculate file hash
        hashes['file_hash'] = calculate_file_hash(file_path)
        
        # Calculate content hash
        hashes['content_hash'] = calculate_content_hash(file_path)
        
        # Calculate metadata hash if metadata provided
        if metadata:
            hashes['metadata_hash'] = calculate_metadata_hash(metadata)
            
    except Exception as e:
        print(f"Error calculating hashes for {file_path}: {e}")
        
    return hashes


def compare_hashes(hash1: Optional[str], hash2: Optional[str]) -> bool:
    """
    Compare two hashes for equality.
    
    Args:
        hash1: First hash string
        hash2: Second hash string
        
    Returns:
        True if hashes match, False otherwise
    """
    if hash1 is None or hash2 is None:
        return False
    return hash1.lower() == hash2.lower()


def get_similarity_score(content_hash1: Optional[str], content_hash2: Optional[str]) -> float:
    """
    Calculate similarity score between two content hashes.
    This is a simple implementation - could be enhanced with more sophisticated algorithms.
    
    Args:
        content_hash1: First content hash
        content_hash2: Second content hash
        
    Returns:
        Similarity score between 0.0 and 1.0
    """
    if not content_hash1 or not content_hash2:
        return 0.0
        
    if content_hash1 == content_hash2:
        return 1.0
        
    # Simple character-based similarity
    # In production, you might want to use more sophisticated algorithms
    try:
        matches = sum(c1 == c2 for c1, c2 in zip(content_hash1, content_hash2))
        total_chars = max(len(content_hash1), len(content_hash2))
        return matches / total_chars if total_chars > 0 else 0.0
    except Exception:
        return 0.0


def validate_hash_format(hash_string: str, algorithm: str = "sha256") -> bool:
    """
    Validate if a hash string has the correct format for the specified algorithm.
    
    Args:
        hash_string: Hash string to validate
        algorithm: Hash algorithm ("md5", "sha256", "sha1")
        
    Returns:
        True if hash format is valid, False otherwise
    """
    if not hash_string:
        return False
        
    expected_lengths = {
        'md5': 32,
        'sha1': 40,
        'sha256': 64
    }
    
    expected_length = expected_lengths.get(algorithm.lower())
    if not expected_length:
        return False
        
    # Check length and hex format
    if len(hash_string) != expected_length:
        return False
        
    try:
        int(hash_string, 16)  # Validate hex format
        return True
    except ValueError:
        return False
