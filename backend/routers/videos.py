from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import shutil
import uuid
from datetime import datetime

from models.database import get_db, Video, Tag, video_tags
from models.schemas import (
    VideoResponse, VideoUpdate, UploadResponse, VideoFilter,
    PaginationParams, ProcessingJobResponse, DownloadRequest, DownloadResponse
)
from services.video_service import VideoService
from services.processing_service import ProcessingService
from services.download_service import DownloadService

router = APIRouter()

@router.get("/")
async def get_videos(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    tags: Optional[str] = Query(None, description="Comma-separated tag names"),
    search: Optional[str] = Query(None, description="Search in title and transcript"),
    language: Optional[str] = Query(None),
    processed: Optional[bool] = Query(None),
    category: Optional[str] = Query(None, description="Filter by category (Business, Tech, Design, Cooking, Travel, etc.)"),
    db: Session = Depends(get_db)
):
    """Get list of videos with optional filtering"""
    video_service = VideoService(db)

    # Parse tags filter
    tag_list = tags.split(",") if tags else None

    # Create filter object
    filters = VideoFilter(
        tags=tag_list,
        search=search if search and search.lower() != 'none' else None,
        language=language,
        processed=processed,
        category=category
    )

    videos = video_service.get_videos(skip=skip, limit=limit, filters=filters)

    # Convert to dict manually to avoid Pydantic serialization issues
    result = []
    for video in videos:
        try:
            video_dict = {
                "id": video.id,
                "title": video.title,
                "original_filename": video.original_filename,
                "filename": video.filename,
                "file_path": video.file_path,
                "file_size": video.file_size,
                "duration": video.duration,
                "width": video.width,
                "height": video.height,
                "fps": video.fps,
                "thumbnail_path": video.thumbnail_path,
                "transcript": video.transcript,
                "transcript_language": video.transcript_language,
                "upload_date": video.upload_date.isoformat() if video.upload_date else None,
                "processed": video.processed,
                "processing_status": video.processing_status,
                "processing_progress": video.processing_progress,
                "tags": [
                    {
                        "id": tag.id,
                        "name": tag.name,
                        "color": tag.color,
                        "description": tag.description,
                        "created_date": tag.created_date.isoformat() if tag.created_date else None,
                        "usage_count": tag.usage_count
                    }
                    for tag in video.tags
                ]
            }
            result.append(video_dict)
        except Exception as e:
            print(f"Error serializing video {video.id}: {e}")
            # Skip videos that can't be serialized
            continue

    return result

@router.get("/categories")
async def get_categories(db: Session = Depends(get_db)):
    """Get available video categories"""
    from sqlalchemy import distinct

    # Get all unique categories from tag descriptions
    categories = db.query(distinct(Tag.description)).all()

    # Extract category names (everything before the colon)
    category_names = set()
    for (description,) in categories:
        if description and ':' in description:
            category = description.split(':')[0].strip()
            if category:
                category_names.add(category)

    # Sort categories
    sorted_categories = sorted(list(category_names))

    return {
        "categories": sorted_categories,
        "total": len(sorted_categories)
    }







@router.get("/{video_id}", response_model=VideoResponse)
async def get_video(video_id: int, db: Session = Depends(get_db)):
    """Get a specific video by ID"""
    video_service = VideoService(db)
    video = video_service.get_video_by_id(video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    return video

@router.put("/{video_id}", response_model=VideoResponse)
async def update_video(
    video_id: int, 
    video_update: VideoUpdate, 
    db: Session = Depends(get_db)
):
    """Update video metadata"""
    video_service = VideoService(db)
    video = video_service.update_video(video_id, video_update)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    return video

@router.delete("/{video_id}")
async def delete_video(video_id: int, db: Session = Depends(get_db)):
    """Delete a video and its associated files"""
    video_service = VideoService(db)
    success = video_service.delete_video(video_id)
    if not success:
        raise HTTPException(status_code=404, detail="Video not found")
    return {"message": "Video deleted successfully"}

@router.post("/upload", response_model=UploadResponse)
async def upload_videos(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db)
):
    """Upload multiple video files"""
    video_service = VideoService(db)
    processing_service = ProcessingService(db)
    
    uploaded_files = []
    failed_files = []
    
    for file in files:
        try:
            # Validate file type
            if not file.content_type.startswith('video/'):
                failed_files.append({
                    "filename": file.filename,
                    "error": "Invalid file type. Only video files are allowed."
                })
                continue
            
            # Generate unique filename
            file_extension = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join("videos", unique_filename)
            
            # Save file
            os.makedirs("videos", exist_ok=True)
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            # Create video record
            video = video_service.create_video(
                filename=unique_filename,
                original_filename=file.filename,
                file_path=file_path,
                file_size=file_size
            )
            
            # Queue processing tasks
            background_tasks.add_task(
                processing_service.process_video_async,
                video.id
            )
            
            uploaded_files.append(file.filename)
            
        except Exception as e:
            failed_files.append({
                "filename": file.filename,
                "error": str(e)
            })
    
    return UploadResponse(
        message=f"Uploaded {len(uploaded_files)} files successfully",
        uploaded_files=uploaded_files,
        failed_files=failed_files,
        total_uploaded=len(uploaded_files)
    )

@router.post("/download", response_model=DownloadResponse)
async def download_video(
    download_request: DownloadRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Download video from URL"""
    download_service = DownloadService(db)
    processing_service = ProcessingService(db)

    try:
        # Validate URL format first
        if not download_service.validate_url(download_request.url):
            raise HTTPException(
                status_code=400,
                detail="Invalid or unsupported URL. Supported sites: YouTube, Vimeo, TikTok, Instagram, Twitter"
            )

        # Check if video is accessible
        try:
            video_info = download_service.get_video_info(download_request.url)
            if not video_info:
                raise HTTPException(
                    status_code=400,
                    detail="Unable to access video. It may be private, deleted, or geo-blocked."
                )
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Error checking video accessibility: {str(e)}"
            )

        # Create a placeholder video record first to get an ID
        from models.database import Video
        import uuid

        # Generate unique filename for placeholder
        unique_id = str(uuid.uuid4())
        placeholder_filename = f"downloading_{unique_id}"

        placeholder_video = Video(
            filename=placeholder_filename,
            original_filename="downloading...",
            title="Downloading...",
            file_path="",
            file_size=0,
            source_url=download_request.url,
            download_status="downloading",
            processing_status="pending"
        )

        db.add(placeholder_video)
        db.commit()
        db.refresh(placeholder_video)

        # Start download in background
        async def download_and_process():
            try:
                video = await download_service.download_video_async(
                    url=download_request.url,
                    quality=download_request.quality,
                    format_selector=download_request.format
                )

                if video:
                    # Remove placeholder and use the real video
                    db.delete(placeholder_video)
                    db.commit()

                    # Queue for processing (transcription + tagging)
                    await processing_service.process_video_async(video.id)

            except Exception as e:
                print(f"Download and processing failed: {e}")
                # Update placeholder with error
                placeholder_video.download_status = "failed"
                placeholder_video.download_error = str(e)
                db.commit()

        # Add download task to background
        background_tasks.add_task(download_and_process)

        return DownloadResponse(
            message="Download started successfully",
            video_id=placeholder_video.id,
            download_status="downloading",
            url=download_request.url
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")

@router.get("/{video_id}/download-status")
async def get_download_status(video_id: int, db: Session = Depends(get_db)):
    """Get download status for a video"""
    download_service = DownloadService(db)
    status = download_service.get_download_status(video_id)

    if not status:
        raise HTTPException(status_code=404, detail="Video not found")

    return status

@router.get("/{video_id}/transcript")
async def get_video_transcript(video_id: int, db: Session = Depends(get_db)):
    """Get video transcript"""
    video_service = VideoService(db)
    video = video_service.get_video_by_id(video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if not video.transcript:
        raise HTTPException(status_code=404, detail="Transcript not available")
    
    return {
        "video_id": video_id,
        "transcript": video.transcript,
        "language": video.transcript_language
    }

@router.post("/{video_id}/reprocess")
async def reprocess_video(
    video_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Reprocess a video (transcription and tagging)"""
    video_service = VideoService(db)
    processing_service = ProcessingService(db)
    
    video = video_service.get_video_by_id(video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Queue reprocessing
    background_tasks.add_task(
        processing_service.process_video_async,
        video_id
    )
    
    return {"message": "Video reprocessing queued"}

@router.get("/{video_id}/download")
async def download_video(video_id: int, db: Session = Depends(get_db)):
    """Download video file"""
    video_service = VideoService(db)
    video = video_service.get_video_by_id(video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if not os.path.exists(video.file_path):
        raise HTTPException(status_code=404, detail="Video file not found")
    
    return FileResponse(
        video.file_path,
        filename=video.original_filename,
        media_type='application/octet-stream'
    )

@router.get("/file/{filename}")
async def get_video_file(filename: str):
    """Serve video file"""
    file_path = os.path.join("videos", filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Video file not found")

    return FileResponse(
        file_path,
        media_type='video/mp4',
        headers={"Accept-Ranges": "bytes"}
    )

@router.get("/thumbnail/{filename}")
async def get_thumbnail_file(filename: str):
    """Serve thumbnail file"""
    file_path = os.path.join("videos", "thumbnails", filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Thumbnail file not found")

    return FileResponse(
        file_path,
        media_type='image/jpeg'
    )

@router.post("/cleanup-tags")
async def cleanup_tags(db: Session = Depends(get_db)):
    """Recalculate tag usage counts and clean up unused tags"""
    video_service = VideoService(db)
    video_service.recalculate_tag_usage_counts()
    return {"message": "Tag usage counts recalculated and unused tags cleaned up"}
